# 📌 Augment Prompt: Tool Usage Instructions for Project Playtime Engine

## 🌟 Purpose
This prompt integrates frequent tool usage with the Project Playtime Engine.

## 🎯 Instructions
- Scan the user's settings.json file to detect available MCP servers, their tool usage, entry points, and activation triggers.
- Document MCP server details including tool names, expected inputs/outputs, and call protocols.
- Emphasize frequent tool invocation in every project phase: planning, implementation, debugging, testing, and optimization.
- Prioritize calling tools proactively, even if not explicitly requested by the user.
- Maintain a comprehensive log of all tool interactions.
- Adjust tool calls based on context: file types, project module focus, and runtime conditions.
- Include fallback tool options if a primary tool fails.
- Optimize for C++ and game engine development tasks, particularly those in rendering, physics, audio, and animation subsystems.
- Where possible, integrate tool outputs into build/test pipelines.
- Reference configuration settings and context notes from user settings.json to align tool actions.
- Document edge cases where tool usage is limited and provide alternative steps.

## 📚 Resources
- User settings.json for MCP configuration.
- Project Playtime Engine documentation.

# 🌈 End of Prompt
