# Project Playtime Engine

A next-generation high-performance game engine supporting 2D, 2.5D, and 3D game development with equal proficiency. Built for cross-platform compatibility (Windows and Linux) with a focus on real-time photorealism and stylized visuals.

## Features

### Multi-Dimensional Support
- **2D Games**: Native sprite rendering, 2D physics, and optimized 2D workflows
- **2.5D Games**: Seamless blending of 2D sprites with 3D environments
- **3D Games**: Cutting-edge 3D rendering with ray tracing and advanced lighting

### Core Subsystems
- **Rendering**: Unified pipeline supporting 2D sprites and 3D meshes with modern GPU features
- **Physics**: Both 2D (Box2D-style) and 3D physics simulation
- **Animation**: Sprite-based animation and skeletal animation systems
- **Audio**: 3D spatial audio with cross-platform support
- **Scene Management**: ECS-based architecture with efficient spatial partitioning
- **Multithreading**: Job system for maximum CPU utilization
- **Memory Management**: Custom allocators optimized for game workloads
- **Plugin Architecture**: Extensible design for custom features

### Cross-Platform
- **Windows**: DirectX 12, DLSS, XAudio2
- **Linux**: Vulkan, FSR, OpenAL
- Feature parity across platforms

## Documentation

See [`Project Playtime Engine.md`](./Project%20Playtime%20Engine.md) for comprehensive technical documentation, research, and implementation strategy.

## Getting Started

This project is currently in the research and planning phase. The engine architecture and implementation strategy are documented in detail in the main documentation file.

### Prerequisites
- C++20 compatible compiler
- CMake 3.20+
- Platform-specific SDKs (DirectX 12 SDK for Windows, Vulkan SDK for Linux)

### Building
*Build instructions will be added as development progresses.*

## Architecture Highlights

- **ECS (Entity Component System)**: Using EnTT or Flecs for high-performance data-oriented design
- **Modern Graphics APIs**: DirectX 12 and Vulkan for maximum GPU utilization
- **Job System**: Multi-threaded task scheduling for CPU parallelism
- **Custom Memory Management**: Specialized allocators for different usage patterns
- **Plugin System**: Dynamic loading of game-specific or third-party modules

## Supported Technologies

### Graphics
- Ray Tracing (DXR, Vulkan RT)
- DLSS, FSR, XeSS upscaling
- PBR (Physically Based Rendering)
- Temporal Anti-Aliasing (TAA)
- Screen-Space Reflections (SSR)

### Physics
- 3D: PhysX, Jolt, or Bullet Physics
- 2D: Box2D integration
- Continuous Collision Detection (CCD)
- Deterministic simulation

### Audio
- 3D spatial audio
- Real-time effects processing
- Cross-platform audio backends

## License

*License information will be added.*

## Contributing

*Contribution guidelines will be added as the project develops.*

---

**Note**: This project is currently in the research and design phase. The documentation represents the planned architecture and features for the engine.
